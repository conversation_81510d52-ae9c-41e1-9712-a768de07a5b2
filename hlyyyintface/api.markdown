# 随访模块API接口文档

## 版本历史

### v1.2.5 - 2025-07-25
**C端问卷接口格式统一**:
- 修改三个核心C端问卷接口，统一返回格式符合前端期望
- 实现系统字段与C端期望字段的专门映射逻辑
- 支持examId、examType、qId、type等C端标准字段格式
- 优化result字段处理，支持多选题数组和单选题字符串格式

**接口变更**:
- `/emr/followup/customer/questionnaire-list` - 返回C端期望格式
- `/emr/followup/customer/questionnaire-submit` - 接收C端格式数据
- `/emr/followup/customer/questionnaire-answers` - 返回包含result的完整结构

**技术改进**:
- 新增buildCustomerQuestionnaireList等C端专用方法
- 实现题目类型转换(single->radio, multiple->checkbox)
- 优化答案格式处理和数据映射逻辑
- 保持向后兼容，不影响管理端接口

### v1.2.4 - 2025-07-24 21:30
**数据库结构同步更新**:
- 根据ylydb.sql文件同步数据库表结构
- 更新所有表的AUTO_INCREMENT值和实际数据记录
- 验证字段类型、索引、外键约束与实际数据库一致
- 确保API接口与数据库结构完全匹配

**技术改进**:
- 同步26张表的完整结构定义
- 验证所有字段类型、长度、默认值
- 确保索引定义和外键约束正确
- 添加实际示例数据记录

**影响接口**:
- 所有随访模块相关接口
- 数据库结构参考文档
- 开发人员理解数据库结构

### v1.2.3 - 2025-01-23 10:00
**数据库表结构支持**:
- 新增 `questionnaire_answer` 表支持随访计划问卷填报
- 实现智能表单问卷和随访计划问卷的数据分离
- 支持周期性随访任务的问卷填报功能

**技术改进**:
- 通过 `task_id` 关联具体的随访任务
- 添加完整的外键约束确保数据一致性
- 优化索引设计提高查询性能

**影响接口**:
- `/emr/followup/customer/questionnaire-submit` - 问卷填报接口
- `/emr/followup/customer/questionnaire-answers` - 问卷答案查看接口

### v1.2.2 - 2024-12-19 18:30
**更新内容：**
- 修复随访计划详情接口的数据库字段错误
- 更新接口文档，说明字段查询方式
- 添加MyBatis-Plus查询方法说明

### v1.2.1 - 2024-12-19 14:30
**更新内容：**
- 完善表单收集相关API接口文档
- 添加问卷管理接口说明
- 优化接口响应格式说明

### v1.2.0 - 2024-12-19 10:15
**更新内容：**
- 添加表单模板管理接口
- 完善问卷关联接口
- 优化错误处理说明

### v1.1.0 - 2024-12-18 16:45
**更新内容：**
- 初始版本发布
- 基础随访计划接口
- 患者分组管理接口

## 基础信息

- **基础URL**: `/api/emr/followup`
- **请求方式**: RESTful API
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-12-19T14:30:00"
}
```

## 随访计划管理接口

### 1. 获取随访计划列表

**接口地址**: `GET /api/emr/followup/plan/page`

**请求参数**:
```json
{
  "current": 1,
  "size": 10,
  "name": "",
  "category": "",
  "status": ""
}
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "name": "高血压随访计划",
        "description": "针对高血压患者的长期随访计划",
        "category": "chronic_disease",
        "status": "active",
        "patientCount": 5,
        "taskCount": 10,
        "completedTaskCount": 8,
        "createTime": "2024-12-19T10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取随访计划详情

**接口地址**: `GET /api/emr/followup/plan/{id}`

**响应数据**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "高血压随访计划",
    "description": "针对高血压患者的长期随访计划",
    "category": "chronic_disease",
    "status": "active",
    "subplans": [
      {
        "id": 1,
        "name": "子计划1",
        "type": "patient",
        "executionTimeType": "all",
        "reminderTime": "09:00",
        "planType": "cycle",
        "items": [],
        "questionnaires": []
      }
    ]
  }
}
```

### 3. 创建随访计划

**接口地址**: `POST /api/emr/followup/plan`

**请求数据**:
```json
{
  "name": "新随访计划",
  "description": "计划描述",
  "category": "chronic_disease",
  "groupId": 1,
  "subplans": []
}
```

### 4. 更新随访计划

**接口地址**: `PUT /api/emr/followup/plan`

**请求数据**:
```json
{
  "id": 1,
  "name": "更新后的计划名称",
  "description": "更新后的描述",
  "category": "chronic_disease",
  "subplans": []
}
```

### 5. 删除随访计划

**接口地址**: `DELETE /api/emr/followup/plan/{id}`

## 表单模板管理接口

### 1. 创建表单模板

**接口地址**: `POST /api/emr/followup/form-template`

**请求数据**:
```json
{
  "name": "表单模板名称",
  "formType": "one_time",
  "questionnaires": [
    {
      "questionnaireId": 1,
      "sortOrder": 1,
      "isRequired": true,
      "displayConfig": {
        "title": "显示标题",
        "description": "显示描述"
      }
    }
  ],
  "otherData": [],
  "remarkText": "备注说明",
  "consentConfig": {
    "enabled": true
  }
}
```

### 2. 更新表单模板

**接口地址**: `PUT /api/emr/followup/form-template`

**请求数据**: 同创建接口

### 3. 获取表单模板详情

**接口地址**: `GET /api/emr/followup/form-template/{id}`

### 4. 删除表单模板

**接口地址**: `DELETE /api/emr/followup/form-template/{id}`

## 问卷管理接口

### 1. 获取问卷列表

**接口地址**: `GET /api/emr/followup/questionnaire/page`

**请求参数**:
```json
{
  "current": 1,
  "size": 10,
  "name": "",
  "type": ""
}
```

**响应数据**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "name": "疼痛评估问卷",
        "type": "symptom",
        "questionCount": 10,
        "description": "疼痛相关不适评估"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1
  }
}
```

### 2. 添加表单问卷

**接口地址**: `POST /api/emr/followup/form-questionnaire`

**请求数据**:
```json
{
  "formTemplateId": 1,
  "questionnaireId": 1,
  "sortOrder": 1,
  "isRequired": true,
  "displayConfig": {
    "title": "显示标题",
    "description": "显示描述"
  }
}
```

### 3. 更新表单问卷

**接口地址**: `PUT /api/emr/followup/form-questionnaire`

**请求数据**:
```json
{
  "id": 1,
  "sortOrder": 2,
  "isRequired": false,
  "displayConfig": {
    "title": "更新后的标题",
    "description": "更新后的描述"
  }
}
```

### 4. 移除表单问卷

**接口地址**: `DELETE /api/emr/followup/form-questionnaire/{id}`

## 患者分组管理接口

### 1. 获取患者分组列表

**接口地址**: `GET /api/emr/followup/patient-group/page`

**请求参数**:
```json
{
  "current": 1,
  "size": 10,
  "name": ""
}
```

### 2. 创建患者分组

**接口地址**: `POST /api/emr/followup/patient-group`

**请求数据**:
```json
{
  "name": "高血压患者组",
  "description": "高血压患者分组",
  "patientIds": [1, 2, 3]
}
```

### 3. 更新患者分组

**接口地址**: `PUT /api/emr/followup/patient-group`

### 4. 删除患者分组

**接口地址**: `DELETE /api/emr/followup/patient-group/{id}`

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查登录状态 |
| 403 | 权限不足 | 联系管理员 |
| 404 | 资源不存在 | 检查资源ID |
| 500 | 服务器内部错误 | 联系技术支持 |

## 常见问题

### Q: 表单收集按钮位置不正确
**A:** 已修复，通过调整z-index和定位设置解决

### Q: 问卷配置保存失败
**A:** 检查数据格式是否正确，确保所有必填字段都已填写

### Q: 下拉菜单被遮挡
**A:** 已修复，设置正确的z-index层级

## 更新日志

- 2024-12-19 14:30: 完善表单收集相关API接口文档
- 2024-12-19 10:15: 添加表单模板管理接口
- 2024-12-18 16:45: 初始版本发布

---

*最后更新时间: 2024-12-19 14:30*