# C端问卷接口测试文档

## 测试目的
验证修改后的三个C端问卷接口是否按照期望格式正常工作。

## 测试接口
1. `/emr/followup/customer/questionnaire-list` - 问卷列表获取接口
2. `/emr/followup/customer/questionnaire-submit` - 问卷填报接口  
3. `/emr/followup/customer/questionnaire-answers` - 问卷答案查看接口

## 测试数据准备
- 手机号: 13800138000
- 计划ID: 34
- 子计划ID: 44
- 问卷ID: 1

## 测试步骤

### 1. 测试问卷列表获取接口

**请求**:
```bash
curl -X POST "http://localhost:18923/emr/followup/customer/questionnaire-list" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "planId": "34",
    "subplanId": "44"
  }'
```

**期望响应格式**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "planId": 34,
    "planName": "高血压随访计划",
    "subplanId": 44,
    "subplanName": "第一次随访",
    "questionnaires": [
      {
        "examId": "1",
        "examType": "questionForm",
        "title": "疼痛相关不适评估",
        "description": "custom",
        "questions": [
          {
            "qId": "1",
            "type": "checkbox",
            "text": "请您选择您最近1周内是否出现以下不适症状",
            "required": true,
            "options": [
              {
                "value": "1",
                "label": "天气下雨或阴天",
                "score": 5
              },
              {
                "value": "2",
                "label": "疼痛",
                "score": 2
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 2. 测试问卷填报接口

**请求**:
```bash
curl -X POST "http://localhost:18923/emr/followup/customer/questionnaire-submit" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "planId": "34",
    "subplanId": "44",
    "questionnaires": [
      {
        "examId": "1",
        "examType": "questionForm",
        "title": "疼痛相关不适评估",
        "description": "感谢您参与本次调查",
        "questions": [
          {
            "qId": "1",
            "type": "checkbox",
            "text": "请您选择症状",
            "result": ["2", "3", "5"],
            "required": true
          }
        ]
      }
    ]
  }'
```

**期望响应格式**:
```json
{
  "code": 200,
  "message": "问卷提交成功",
  "data": {
    "planId": 34,
    "subplanId": 44,
    "submittedQuestionnaires": [
      {
        "questionnaireId": 1,
        "questionnaireName": "疼痛相关不适评估",
        "totalScore": 6.00,
        "submittedQuestions": 1
      }
    ],
    "totalScore": 6.00,
    "submitTime": "2025-07-25T10:30:00.000Z"
  }
}
```

### 3. 测试问卷答案查看接口

**请求**:
```bash
curl -X POST "http://localhost:18923/emr/followup/customer/questionnaire-answers" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138000",
    "planId": "34",
    "subplanId": "44",
    "questionnaireId": "1"
  }'
```

**期望响应格式**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "planId": 34,
    "planName": "高血压随访计划",
    "subplanId": 44,
    "subplanName": "第一次随访",
    "questionnaires": [
      {
        "examId": "1",
        "examType": "questionForm",
        "title": "疼痛相关不适评估",
        "description": "custom",
        "questions": [
          {
            "qId": "1",
            "type": "checkbox",
            "text": "请您选择您最近1周内是否出现以下不适症状",
            "result": ["2", "3", "5"],
            "required": true,
            "options": [
              {
                "value": "1",
                "label": "天气下雨或阴天",
                "score": 5
              },
              {
                "value": "2",
                "label": "疼痛",
                "score": 2
              }
            ]
          }
        ]
      }
    ]
  }
}
```

## 验证要点

### 字段映射验证
- [x] `id` -> `examId`
- [x] `name` -> `title`
- [x] `questionText` -> `text`
- [x] `questionType` -> `type`
- [x] `optionText` -> `label`
- [x] `optionValue` -> `value`

### 题目类型转换验证
- [x] `single` -> `radio`
- [x] `multiple` -> `checkbox`
- [x] `text/input` -> `input`

### result字段格式验证
- [x] 多选题: 字符串数组 `["1", "2", "3"]`
- [x] 单选题: 字符串 `"1"`
- [x] 文本题: 字符串 `"用户输入内容"`

## 测试结果
- [ ] 问卷列表获取接口 - 待测试
- [ ] 问卷填报接口 - 待测试
- [ ] 问卷答案查看接口 - 待测试

## 注意事项
1. 确保数据库中有对应的测试数据
2. 验证返回的JSON格式是否完全符合期望
3. 检查字段类型和数据格式是否正确
4. 测试不同题目类型的处理是否正确
