# 随访模块表设计文档（数据库同步版本）

**更新时间**: 2025-07-24  
**版本**: v3.4.0

## 变更说明

本文档已根据实际数据库结构（随访.sql）进行同步更新，确保文档与数据库表结构完全一致。

### 最新更新（2025-07-24 v3.4.0）
- **数据库结构同步**: 根据ylydb.sql文件更新文档，确保与实际数据库结构完全一致
- **表数据更新**: 更新了所有表的AUTO_INCREMENT值
- **字段类型验证**: 验证所有字段类型、长度、默认值与数据库保持一致
- **索引完整性**: 确保所有索引定义与实际数据库一致
- **外键约束验证**: 验证所有外键约束关系正确
- **文档清理**: 移除了示例数据记录，保持文档简洁

### 历史更新（2025-01-23 v3.2.1）
- **患者入组审核联动功能**: 实现了患者入组审核通过时自动更新相关随访计划患者数量的功能
- **业务逻辑优化**: 在PatientServiceImpl中添加了状态更新与随访计划统计的联动机制
- **实时统计更新**: 审核通过后立即更新相关随访计划的患者数量，确保统计信息的实时性和准确性
- **批量操作支持**: 支持单个患者和批量患者的审核操作，都能正确更新相关统计信息
- **典型场景覆盖**: 覆盖"先创建分组→再创建随访计划→患者入组审核通过"的业务场景

### 历史更新（2025-01-21 v3.2.0）
- **随访计划表统计字段**: 为followup_plan表添加了patient_count（患者数量）、task_count（任务数量）、completed_task_count（已完成任务数量）三个统计字段
- **统计字段索引**: 为新增的统计字段添加了对应的索引，提高查询性能
- **统计信息联动**: 修复了随访计划列表中患者数量显示为0的问题，实现统计信息与患者分组的正确联动
- **API接口扩展**: 添加了手动更新统计信息的API接口，支持单个计划和批量更新
- **数据库脚本**: 提供了add_statistics_fields.sql脚本，用于数据库表结构更新

### 历史更新（2025-01-23 v3.1.0）
- **C端用户表结构修复**: 修复了followup_c_user表缺失字段问题，添加了age、id_type、relationship、id_number、jhr_id_type、jhr_id_number、jhr_name、jhr_phone、is_bind、is_auth等字段
- **实体类字段映射**: 为FollowupCUser实体类添加了@TableField注解，确保字段正确映射
- **MyBatis-Plus配置优化**: 调整了auto-mapping-unknown-column-behavior配置，提高错误处理能力
- **日志配置完善**: 添加了详细的日志配置和全局异常处理器，便于问题排查
- **数据库结构同步**: 根据实际数据库表结构更新文档，确保完全一致
- **字段类型统一**: 所有字段类型与数据库实际定义保持一致
- **索引优化**: 添加了数据库中的实际索引定义
- **外键约束**: 完善了所有外键约束关系
- **表结构完整性**: 确保每个表的字段、类型、注释都与数据库一致

### 变更原则
- **准确性优先**: 文档内容必须与数据库实际结构完全一致
- **完整性保证**: 包含所有表、字段、索引、约束的完整定义
- **实用性导向**: 提供可直接执行的SQL语句
- **维护便利**: 便于后续的数据库维护和功能开发

## 1. 内容模板相关表

### 1.1 内容模板表 (content_template)
```sql
CREATE TABLE `content_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板说明',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板分类（health_education:健康教育, medication_guide:用药指导, nursing_guide:护理指导, rehabilitation_guide:康复指导, diet_guide:饮食指导, other:其他）',
  `message_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'patientEducation' COMMENT '消息类型（patientEducation:患教内容）',
  `permission` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all' COMMENT '权限范围（all:全部用户, self:仅本人, department:同科室）',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板内容（富文本HTML）',
  `content_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容（纯文本，用于搜索）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态（active:启用, inactive:停用）',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `creator_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人科室ID',
  `creator_dept_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人科室名称',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime NULL DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_creator_dept_id`(`creator_dept_id`) USING BTREE,
  INDEX `idx_permission`(`permission`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  FULLTEXT INDEX `ft_content_text`(`content_text`, `title`, `name`) COMMENT '全文搜索索引'
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '内容模板表' ROW_FORMAT = Dynamic;
```

### 1.2 内容模板分类表 (content_template_category)
```sql
CREATE TABLE `content_template_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类说明',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态（active:启用, inactive:停用）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板分类字典表' ROW_FORMAT = Dynamic;
```

### 1.3 内容模板收藏表 (content_template_favorite)
```sql
CREATE TABLE `content_template_favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_template_user`(`template_id`, `user_id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  CONSTRAINT `fk_favorite_template` FOREIGN KEY (`template_id`) REFERENCES `content_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板收藏表' ROW_FORMAT = Dynamic;
```

### 1.4 内容模板标签表 (content_template_tag)
```sql
CREATE TABLE `content_template_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签颜色',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签说明',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板标签表' ROW_FORMAT = Dynamic;
```

### 1.5 内容模板标签关联表 (content_template_tag_rel)
```sql
CREATE TABLE `content_template_tag_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_template_tag`(`template_id`, `tag_id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_tag_id`(`tag_id`) USING BTREE,
  CONSTRAINT `fk_rel_tag` FOREIGN KEY (`tag_id`) REFERENCES `content_template_tag` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_rel_template` FOREIGN KEY (`template_id`) REFERENCES `content_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板标签关联表' ROW_FORMAT = Dynamic;
```

### 1.6 内容模板使用记录表 (content_template_usage_log)
```sql
CREATE TABLE `content_template_usage_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `template_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `user_id` bigint(20) NOT NULL COMMENT '使用人ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '使用人姓名',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '使用人科室ID',
  `dept_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用人科室名称',
  `usage_scene` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用场景（follow_up:随访, consultation:咨询, notification:通知）',
  `patient_id` bigint(20) NULL DEFAULT NULL COMMENT '患者ID（如果适用）',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名（如果适用）',
  `use_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_usage_scene`(`usage_scene`) USING BTREE,
  INDEX `idx_use_time`(`use_time`) USING BTREE,
  CONSTRAINT `fk_usage_template` FOREIGN KEY (`template_id`) REFERENCES `content_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板使用记录表' ROW_FORMAT = Dynamic;
```

## 2. 问卷相关表

### 2.1 问卷表 (questionnaire)
```sql
CREATE TABLE `questionnaire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'custom' COMMENT '问卷类型',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'active' COMMENT '状态',
  `is_scoring` tinyint(1) NULL DEFAULT 0 COMMENT '是否计分问卷',
  `is_guidance` tinyint(1) NULL DEFAULT 0 COMMENT '是否指导问卷',
  `is_trigger` tinyint(1) NULL DEFAULT 0 COMMENT '是否触发问卷',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷表' ROW_FORMAT = Dynamic;
```

### 2.2 问卷题目表 (questionnaire_question)
```sql
CREATE TABLE `questionnaire_question` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目内容',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'single' COMMENT '题目类型',
  `required` tinyint(1) NULL DEFAULT 0 COMMENT '是否必填',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `instruction` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `text_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文本类型',
  `min_value` int(11) NULL DEFAULT NULL COMMENT '最小值',
  `max_value` int(11) NULL DEFAULT NULL COMMENT '最大值',
  `slider_min` int(11) NULL DEFAULT NULL COMMENT '滑动最小值',
  `slider_max` int(11) NULL DEFAULT NULL COMMENT '滑动最大值',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `questionnaire_id`(`questionnaire_id`) USING BTREE,
  CONSTRAINT `questionnaire_question_ibfk_1` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷题目表' ROW_FORMAT = Dynamic;
```

### 2.3 问卷选项表 (questionnaire_option)
```sql
CREATE TABLE `questionnaire_option` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `option_text` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项文本',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `instruction` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `score` int(11) NULL DEFAULT 0 COMMENT '分值',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_id`(`question_id`) USING BTREE,
  CONSTRAINT `questionnaire_option_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷选项表' ROW_FORMAT = Dynamic;
```

### 2.4 问卷规则表 (questionnaire_rule)
```sql
CREATE TABLE `questionnaire_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `rule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则类型',
  `rule_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则值',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则描述',
  `evaluation_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评定名称',
  `score` int(11) NULL DEFAULT 0 COMMENT '分值',
  `reminder_enabled` tinyint(1) NULL DEFAULT 0 COMMENT '是否启用提醒',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_id`(`question_id`) USING BTREE,
  CONSTRAINT `questionnaire_rule_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷规则表' ROW_FORMAT = Dynamic;
```

### 2.5 问卷答案表 (questionnaire_answer)
```sql
CREATE TABLE `questionnaire_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `question_id` bigint(20) NOT NULL COMMENT '问题ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `answer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案类型：single-单选，multiple-多选，text-文本，number-数字，date-日期，slider-滑动',
  `answer_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案文本内容',
  `answer_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案值（选项ID、数字值等）',
  `answer_score` decimal(10,2) NULL DEFAULT NULL COMMENT '答案得分',
  `answer_time` datetime NOT NULL COMMENT '答题时间',
  `time_spent` int(11) NULL DEFAULT NULL COMMENT '答题耗时（秒）',
  `task_id` bigint(20) NULL DEFAULT NULL COMMENT '任务ID（关联随访任务）',
  `subplan_id` bigint(20) NULL DEFAULT NULL COMMENT '子计划ID',
  `plan_id` bigint(20) NULL DEFAULT NULL COMMENT '计划ID',
  `answer_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'manual' COMMENT '答案来源：manual-手动填写，auto-自动填充，import-导入',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_question_id`(`question_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_answer_time`(`answer_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_patient_questionnaire_time`(`patient_id`, `questionnaire_id`, `answer_time`) USING BTREE,
  INDEX `idx_task_question`(`task_id`, `question_id`) USING BTREE,
  CONSTRAINT `fk_questionnaire_answer_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_question` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_task` FOREIGN KEY (`task_id`) REFERENCES `followup_task` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷答案表（随访计划专用）' ROW_FORMAT = Dynamic;
```

## 3. 智能表单相关表

### 3.1 智能表单模板表 (smart_form_template)
```sql
CREATE TABLE `smart_form_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表单ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单描述',
  `form_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'one_time' COMMENT '表单类型：one_time-一次性问卷，repeatable-重复性问卷',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单分类：postoperative_followup-术后随访，chronic_disease-慢病管理，health_assessment-健康评估，satisfaction_survey-满意度调查，symptom_monitoring-症状监测，rehabilitation_assessment-康复评估，other-其他',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `permission` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'self' COMMENT '权限范围：self-仅本人查看，department-本科室查看，all-全院查看',
  `config` json NULL COMMENT '表单配置信息（JSON格式）：showProgress, allowEdit, autoSave, theme, submitBtnText',
  `repeat_config` json NULL COMMENT '重复配置（仅重复性问卷使用）：interval, duration, unit, reminderEnabled, reminderTime',
  `remark_text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注说明（最大50字符）',
  `consent_config` json NULL COMMENT '知情同意书配置（JSON格式）：enabled, content',
  `questionnaire_id` bigint(20) NULL DEFAULT NULL COMMENT '关联问卷ID',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者姓名',
  `creator_dept_id` bigint(20) NOT NULL COMMENT '创建者科室ID',
  `creator_dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者科室名称',
  `submission_count` int(11) NULL DEFAULT 0 COMMENT '提交次数',
  `last_submitted_time` datetime NULL DEFAULT NULL COMMENT '最后提交时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `qr_code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '填写二维码URL',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_creator_dept_id`(`creator_dept_id`) USING BTREE,
  INDEX `idx_form_type`(`form_type`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_permission`(`permission`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单模板表' ROW_FORMAT = Dynamic;
```

### 3.2 智能表单提交表 (smart_form_submission)
```sql
CREATE TABLE `smart_form_submission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `form_template_id` bigint(20) NOT NULL COMMENT '表单模板ID',
  `submission_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提交编号',
  `patient_id` bigint(20) NULL DEFAULT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者手机号',
  `patient_id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者身份证号',
  `submitter_id` bigint(20) NULL DEFAULT NULL COMMENT '提交者ID',
  `submitter_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交者姓名',
  `submitter_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'patient' COMMENT '提交者类型：patient-患者，doctor-医生，nurse-护士',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'submitted' COMMENT '状态：submitted-已提交，reviewed-已审核，completed-已完成',
  `progress` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '完成进度（百分比）',
  `total_score` decimal(10, 2) NULL DEFAULT NULL COMMENT '总分数',
  `submit_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'web' COMMENT '提交来源：web-网页，app-APP，mini-小程序',
  `submit_device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交设备信息',
  `submit_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交IP地址',
  `submit_time` datetime NOT NULL COMMENT '提交时间',
  `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_submission_no`(`submission_no`) USING BTREE,
  INDEX `idx_form_template_id`(`form_template_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_submitter_id`(`submitter_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_submit_time`(`submit_time`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  CONSTRAINT `fk_smart_form_submission_form` FOREIGN KEY (`form_template_id`) REFERENCES `smart_form_template` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单提交记录表' ROW_FORMAT = DYNAMIC;
```

### 3.3 智能表单答案表 (smart_form_answer)
```sql
CREATE TABLE `smart_form_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `submission_id` bigint(20) NOT NULL COMMENT '提交ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `answer_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案值',
  `answer_score` int(11) NULL DEFAULT NULL COMMENT '答案分值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_submission_id`(`submission_id`) USING BTREE,
  INDEX `idx_question_id`(`question_id`) USING BTREE,
  CONSTRAINT `fk_answer_submission` FOREIGN KEY (`submission_id`) REFERENCES `smart_form_submission` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单答案表' ROW_FORMAT = Dynamic;
```

### 3.4 智能表单其他数据表 (smart_form_other_data)
```sql
CREATE TABLE `smart_form_other_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `form_template_id` bigint(20) NOT NULL COMMENT '表单模板ID',
  `data_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据类型：patient_info-患者信息，medical_record-病历信息，lab_result-检验结果，imaging_result-影像结果，medication_info-用药信息，vital_signs-生命体征，other-其他',
  `data_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据名称',
  `data_description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据描述',
  `data_config` json NULL COMMENT '数据配置（JSON格式）',
  `sort_order` int(11) NOT NULL DEFAULT 1 COMMENT '排序顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0-非必填，1-必填',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_template_id`(`form_template_id`) USING BTREE,
  INDEX `idx_data_type`(`data_type`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  CONSTRAINT `fk_smart_form_other_data_form` FOREIGN KEY (`form_template_id`) REFERENCES `smart_form_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单其他数据表' ROW_FORMAT = Dynamic;
```

### 3.5 智能表单问卷关联表 (smart_form_questionnaire_rel)
```sql
CREATE TABLE `smart_form_questionnaire_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `form_template_id` bigint(20) NOT NULL COMMENT '表单模板ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `questionnaire_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷名称（冗余字段，便于搜索）',
  `questionnaire_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷类型：symptom-症状评估，scale-量表评估，side_effect-不良反应，custom-自定义',
  `question_count` int(11) NULL DEFAULT 0 COMMENT '题目数量（冗余字段）',
  `sort_order` int(11) NOT NULL DEFAULT 1 COMMENT '排序顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0-非必填，1-必填',
  `display_config` json NULL COMMENT '显示配置（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_template_id`(`form_template_id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_form_questionnaire`(`form_template_id`, `questionnaire_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单问卷关联表' ROW_FORMAT = Dynamic;
```

### 3.6 智能表单分享配置表 (smart_form_share_config)
```sql
CREATE TABLE `smart_form_share_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `form_id` bigint(20) NOT NULL COMMENT '表单ID',
  `share_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分享类型：qr_code-二维码，link-链接',
  `share_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分享配置JSON',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `max_submissions` int(11) NULL DEFAULT NULL COMMENT '最大提交次数',
  `current_submissions` int(11) NOT NULL DEFAULT 0 COMMENT '当前提交次数',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_id`(`form_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_share_config_form` FOREIGN KEY (`form_id`) REFERENCES `smart_form_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单分享配置表' ROW_FORMAT = Dynamic;
```

## 4. 随访计划相关表

### 4.1 随访计划表 (followup_plan)
```sql
CREATE TABLE `followup_plan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '计划描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划分类：postoperative-术后随访，chronic_disease-慢病管理，health_assessment-健康评估，rehabilitation-康复管理',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，active-启用，paused-暂停，completed-已完成',
  `end_time_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间类型：day-天，week-周，month-月，year-年，unlimited-长期',
  `end_time_value` int(11) NULL DEFAULT NULL COMMENT '结束时间值',
  `group_id` bigint(20) NOT NULL COMMENT '关联的患者分组ID',
  `qr_code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入组申请二维码URL',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `patient_count` int(11) NOT NULL DEFAULT 0 COMMENT '患者数量',
  `task_count` int(11) NOT NULL DEFAULT 0 COMMENT '任务数量',
  `completed_task_count` int(11) NOT NULL DEFAULT 0 COMMENT '已完成任务数量',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE,
  INDEX `idx_patient_count`(`patient_count`) USING BTREE,
  INDEX `idx_task_count`(`task_count`) USING BTREE,
  INDEX `idx_completed_task_count`(`completed_task_count`) USING BTREE,
  CONSTRAINT `fk_plan_group` FOREIGN KEY (`group_id`) REFERENCES `patient_group` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访计划主表' ROW_FORMAT = Dynamic;
```

### 4.2 随访子计划表 (followup_subplan)
```sql
CREATE TABLE `followup_subplan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '子计划ID',
  `plan_id` bigint(20) NOT NULL COMMENT '所属计划ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子计划名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '子计划描述',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子计划类型：patient-患者填写，medical-仅医护填写',
  `execution_time_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行时间类型：all-全部时段，custom-自定义',
  `custom_start_day` int(11) NULL DEFAULT NULL COMMENT '自定义开始天数',
  `custom_end_day` int(11) NULL DEFAULT NULL COMMENT '自定义结束天数',
  `reminder_time` time NOT NULL DEFAULT '09:00:00' COMMENT '提醒时间（精确到分钟）',
  `plan_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划类型：cycle-周期循环，custom-自定义',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE,
  INDEX `idx_plan_type`(`plan_type`) USING BTREE,
  CONSTRAINT `fk_subplan_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访子计划表' ROW_FORMAT = Dynamic;
```

### 4.3 随访子计划项目表 (followup_subplan_item)
```sql
CREATE TABLE `followup_subplan_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `subplan_id` bigint(20) NOT NULL COMMENT '所属子计划ID',
  `item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `cycle_days` int(11) NULL DEFAULT NULL COMMENT '周期天数（周期循环时使用）',
  `join_day` int(11) NULL DEFAULT NULL COMMENT '加入计划第几天（自定义时使用）',
  `event_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件类型：instant_message-即时消息',
  `content_template_id` bigint(20) NULL DEFAULT NULL COMMENT '内容模板ID（即时消息时使用）',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_content_template_id`(`content_template_id`) USING BTREE,
  CONSTRAINT `fk_subplan_item_content_template` FOREIGN KEY (`content_template_id`) REFERENCES `content_template` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_subplan_item_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访子计划项目表' ROW_FORMAT = Dynamic;
```

### 4.4 随访子计划问卷关联表 (followup_subplan_questionnaire)
```sql
CREATE TABLE `followup_subplan_questionnaire` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `subplan_id` bigint(20) NOT NULL COMMENT '子计划ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否必填',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subplan_questionnaire`(`subplan_id`, `questionnaire_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  CONSTRAINT `fk_subplan_questionnaire_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_subplan_questionnaire_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访子计划问卷关联表' ROW_FORMAT = Dynamic;
```

### 4.5 随访任务表 (followup_task)
```sql
CREATE TABLE `followup_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `plan_id` bigint(20) NOT NULL COMMENT '计划ID',
  `subplan_id` bigint(20) NOT NULL COMMENT '子计划ID',
  `subplan_item_id` bigint(20) NOT NULL COMMENT '子计划项目ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `scheduled_time` datetime NOT NULL COMMENT '计划执行时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待执行，executing-执行中，completed-已完成，failed-执行失败',
  `message_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '消息是否已发送',
  `message_sent_time` datetime NULL DEFAULT NULL COMMENT '消息发送时间',
  `questionnaire_completed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '问卷是否已完成',
  `questionnaire_completed_time` datetime NULL DEFAULT NULL COMMENT '问卷完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_subplan_item_id`(`subplan_item_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_scheduled_time`(`scheduled_time`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_task_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_task_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_task_subplan_item` FOREIGN KEY (`subplan_item_id`) REFERENCES `followup_subplan_item` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访任务表' ROW_FORMAT = Dynamic;
```

## 5. 患者分组相关表

### 5.1 患者分组表 (patient_group)
```sql
CREATE TABLE `patient_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分组描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组分类（新增字段）',
  `patient_count` int(11) NULL DEFAULT 0 COMMENT '患者数量',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者分组表' ROW_FORMAT = Dynamic;
```

### 5.2 患者分组关系表 (patient_group_relation)
```sql
CREATE TABLE `patient_group_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `group_id` bigint(20) NOT NULL COMMENT '分组ID',
  `patient_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者手机号',
  `join_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入组方式',
  `user_agree` tinyint(1) NULL DEFAULT NULL COMMENT '用户是否同意',
  `doctor_agree` tinyint(1) NULL DEFAULT NULL COMMENT '医生是否同意',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `agree_time` datetime NULL DEFAULT NULL COMMENT '同意时间',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `auditor_id` bigint(20) NULL DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人姓名',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '入组状态: 0-待审核, 1-已入组, 2-已拒绝',
  `join_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '加入方式: 院内渠道, 院外渠道',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者姓名',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_patient_group`(`patient_id`, `group_id`) USING BTREE COMMENT '患者和分组关系是唯一的',
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者分组关联表' ROW_FORMAT = DYNAMIC;
```

## 6. C端用户相关表

### 6.1 C端用户表 (followup_c_user)
```sql
CREATE TABLE `followup_c_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别：male-男，female-女',
  `age` int(11) NULL DEFAULT NULL COMMENT '年龄',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `emergency_contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急联系人电话',
  `bind_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'unbound' COMMENT '绑定状态',
  `auth_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'unauthorized' COMMENT '授权状态',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `id_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型：0-身份证，1-护照，2-军官证',
  `relationship` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关系：1-本人，2-家属，3-监护人',
  `id_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号码',
  `jhr_id_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人证件类型',
  `jhr_id_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人证件号码',
  `jhr_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人姓名',
  `jhr_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人电话',
  `is_bind` int(11) NULL DEFAULT 0 COMMENT '是否绑定：1-已绑定，0-未绑定',
  `is_auth` int(11) NULL DEFAULT 0 COMMENT '是否授权：1-已授权，0-未授权',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  INDEX `idx_bind_status`(`bind_status`) USING BTREE,
  INDEX `idx_auth_status`(`auth_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'C端用户表' ROW_FORMAT = Dynamic;
```

## 7. 智能随访模板表

### 7.1 智能随访模板表 (smart_followup_template)
```sql
CREATE TABLE `smart_followup_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板分类：1-术后随访，2-慢病管理，3-健康体检',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '科室ID',
  `department_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板配置（JSON格式）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_department_id`(`department_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能随访模板表' ROW_FORMAT = Dynamic;
```

## 8. 表关系图

```
内容模板模块：
content_template_category (1) ←→ (N) content_template (1) ←→ (N) content_template_favorite
content_template (1) ←→ (N) content_template_tag_rel (N) ←→ (1) content_template_tag
content_template (1) ←→ (N) content_template_usage_log

问卷模块：
questionnaire (1) ←→ (N) questionnaire_question (1) ←→ (N) questionnaire_option
questionnaire (1) ←→ (N) questionnaire_rule
questionnaire (1) ←→ (N) questionnaire_answer (N) ←→ (1) followup_task

智能表单模块：
smart_form_template (1) ←→ (N) smart_form_submission (1) ←→ (N) smart_form_answer
smart_form_template (1) ←→ (N) smart_form_questionnaire_rel (N) ←→ (1) questionnaire
smart_form_template (1) ←→ (N) smart_form_share_config
smart_form_template (1) ←→ (N) smart_form_other_data

随访计划模块：
patient_group (1) ←→ (N) patient_group_relation (N) ←→ (1) followup_c_user
followup_plan (1) ←→ (1) patient_group
followup_plan (1) ←→ (N) followup_subplan (1) ←→ (N) followup_subplan_item (1) ←→ (N) followup_task
followup_subplan (1) ←→ (N) followup_subplan_questionnaire (N) ←→ (1) questionnaire
followup_subplan_item (N) ←→ (1) content_template
followup_task (1) ←→ (N) questionnaire_answer (N) ←→ (1) followup_c_user

智能随访模板：
smart_followup_template (独立表，用于智能随访功能)
```

## 9. 变更总结

### 9.1 数据库结构同步更新（2025-07-24 v3.4.0）
- **数据库结构完全同步**: 根据ylydb.sql文件更新文档，确保与实际数据库结构完全一致
- **AUTO_INCREMENT值更新**: 更新了所有表的AUTO_INCREMENT值，反映实际数据库状态
- **字段类型验证**: 验证所有字段类型、长度、默认值与数据库保持一致
- **索引完整性**: 确保所有索引定义与实际数据库一致
- **外键约束验证**: 验证所有外键约束关系正确
- **文档清理**: 移除了示例数据记录，保持文档简洁
- **C端用户表修复**: 修复了followup_c_user表缺失字段问题，添加了age、id_type、relationship、id_number、jhr_id_type、jhr_id_number、jhr_name、jhr_phone、is_bind、is_auth等字段
- **实体类映射优化**: 为FollowupCUser实体类添加了@TableField注解，确保字段正确映射到数据库
- **MyBatis-Plus配置**: 调整了auto-mapping-unknown-column-behavior配置，提高错误处理能力
- **完全同步**: 文档内容与数据库实际结构完全一致
- **字段类型统一**: 所有字段类型、长度、默认值与数据库保持一致
- **索引优化**: 包含所有数据库中的实际索引定义
- **外键约束**: 完善了所有外键约束关系
- **字符集统一**: 所有表使用utf8mb4字符集和utf8mb4_general_ci排序规则

### 9.2 表结构特点
- **内容模板表**: 6张表，支持模板管理、分类、标签、收藏、使用记录等功能
- **问卷表**: 5张表，支持问卷创建、题目、选项、规则、答案存储等功能
- **智能表单表**: 6张表，支持表单模板、提交记录、答案、问卷关联、分享配置等功能
- **随访计划表**: 5张表，支持计划管理、子计划、项目、问卷关联、任务执行等功能
- **患者分组表**: 2张表，支持分组管理、患者关联等功能
- **C端用户表**: 1张表，支持C端用户管理
- **智能随访模板表**: 1张表，支持智能随访功能

### 9.3 数据完整性
- **外键约束**: 所有关联关系都有相应的外键约束
- **索引优化**: 为常用查询字段添加了索引，提高查询性能
- **唯一约束**: 对关键字段添加了唯一约束，确保数据一致性
- **默认值**: 为可选字段设置了合理的默认值

### 9.4 功能支持
- **内容模板管理**: 支持模板的创建、编辑、分类、标签、收藏、使用统计
- **问卷管理**: 支持问卷的创建、题目管理、选项配置、规则设置、答案存储
- **智能表单**: 支持表单模板、在线填写、数据收集、分享功能
- **随访计划**: 支持计划创建、子计划配置、任务执行、进度跟踪、周期性问卷填报
- **患者管理**: 支持患者分组、关联管理、审核流程
- **C端用户管理**: 支持用户注册、绑定、授权、入组、个人信息管理、监护人信息管理
- **用户认证**: 支持手机号验证、绑定状态管理、授权状态管理
- **随访参与**: 支持用户加入随访计划、填写问卷、查看随访进度
- **数据分离**: 智能表单问卷和随访计划问卷数据分离存储，支持不同的业务场景

### 9.5 技术特点
- **MySQL 5.7兼容**: 使用MySQL 5.7兼容的语法和特性
- **UTF8MB4支持**: 支持完整的Unicode字符集，包括emoji
- **JSON字段**: 使用JSON字段存储复杂配置数据
- **全文搜索**: 为内容模板添加了全文搜索索引
- **软删除**: 支持软删除机制，保留数据历史

### 9.4 聊天系统表设计 (2025-07-25)
- **聊天室表**: 为每个随访计划创建聊天室，支持患者与医生沟通
- **聊天消息表**: 存储所有聊天消息，支持文本、图片等类型
- **消息状态表**: 记录消息的已读状态，支持消息已读未读功能
- **轮询机制**: 通过接口轮询获取新消息，避免使用复杂的IM组件

### 9.5 状态值格式统一 (2025-01-23)
- **患者入组状态标准化**: 将patient_group_relation表的status字段从字符串格式改为数字格式
  - 原格式: "未审核", "审核通过", "已拒绝"
  - 新格式: "0" (待审核), "1" (已入组), "2" (已拒绝)
- **状态值说明完善**: 新增第12章节，详细说明所有业务状态值
- **文档同步更新**: 确保表结构文档与代码实现保持一致
- **兼容性保证**: 前端代码支持新旧状态格式，确保系统稳定运行

### 9.5 数据库结构同步更新

### 9.6 数据库更新脚本

#### 9.6.1 随访计划表统计字段更新脚本
**文件位置**: `hlyyyintface/db/add_statistics_fields.sql`

**执行时间**: 2025-01-21

**更新内容**:
```sql
-- 为随访计划表添加统计字段
ALTER TABLE `followup_plan` 
ADD COLUMN `patient_count` int(11) NOT NULL DEFAULT 0 COMMENT '患者数量' AFTER `update_time`;

ALTER TABLE `followup_plan` 
ADD COLUMN `task_count` int(11) NOT NULL DEFAULT 0 COMMENT '任务数量' AFTER `patient_count`;

ALTER TABLE `followup_plan` 
ADD COLUMN `completed_task_count` int(11) NOT NULL DEFAULT 0 COMMENT '已完成任务数量' AFTER `task_count`;

-- 添加索引以提高查询性能
ALTER TABLE `followup_plan` 
ADD INDEX `idx_patient_count` (`patient_count`) USING BTREE,
ADD INDEX `idx_task_count` (`task_count`) USING BTREE,
ADD INDEX `idx_completed_task_count` (`completed_task_count`) USING BTREE;

-- 更新现有数据的统计信息
UPDATE followup_plan fp
SET fp.patient_count = (
    SELECT COUNT(DISTINCT pgr.patient_id)
    FROM patient_group_relation pgr
    WHERE pgr.group_id = fp.group_id
    AND pgr.status = '1'
);

UPDATE followup_plan fp
SET fp.task_count = (
    SELECT COUNT(*)
    FROM followup_task ft
    WHERE ft.plan_id = fp.id
),
fp.completed_task_count = (
    SELECT COUNT(*)
    FROM followup_task ft
    WHERE ft.plan_id = fp.id
    AND ft.status = 'completed'
);
```

**影响范围**:
- 随访计划列表页面统计信息显示
- 随访计划详情页面数据展示
- 数据库查询性能优化

**注意事项**:
- 执行前请备份数据库
- 建议在业务低峰期执行
- 执行后需要重启应用服务

#### 9.6.2 问卷答案表创建脚本
**文件位置**: `hlyyyintface/db/add_questionnaire_answer_table.sql`

**执行时间**: 2025-01-23

**更新内容**:
```sql
-- 创建问卷答案表，用于存储随访计划相关的问卷答案数据
CREATE TABLE `questionnaire_answer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `question_id` bigint(20) NOT NULL COMMENT '问题ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `answer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案类型',
  `answer_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案文本内容',
  `answer_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案值',
  `answer_score` decimal(10,2) NULL DEFAULT NULL COMMENT '答案得分',
  `answer_time` datetime NOT NULL COMMENT '答题时间',
  `time_spent` int(11) NULL DEFAULT NULL COMMENT '答题耗时（秒）',
  `task_id` bigint(20) NULL DEFAULT NULL COMMENT '任务ID（关联随访任务）',
  `subplan_id` bigint(20) NULL DEFAULT NULL COMMENT '子计划ID',
  `plan_id` bigint(20) NULL DEFAULT NULL COMMENT '计划ID',
  `answer_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'manual' COMMENT '答案来源',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_question_id`(`question_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_answer_time`(`answer_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_patient_questionnaire_time`(`patient_id`, `questionnaire_id`, `answer_time`) USING BTREE,
  INDEX `idx_task_question`(`task_id`, `question_id`) USING BTREE,
  CONSTRAINT `fk_questionnaire_answer_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_question` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_task` FOREIGN KEY (`task_id`) REFERENCES `followup_task` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷答案表（随访计划专用）' ROW_FORMAT = Dynamic;
```

**影响范围**:
- 随访计划问卷填报功能
- 问卷答案数据存储
- 随访任务状态更新

**注意事项**:
- 执行前请备份数据库
- 确保相关的外键表已存在
- 执行后需要重启应用服务
- 该表与smart_form_answer表分离，支持不同的业务场景